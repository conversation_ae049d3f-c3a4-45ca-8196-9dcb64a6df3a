"""
Terminal chat input component for AI Terminal.

Provides advanced input handling with syntax highlighting, auto-completion,
multi-line editing, and slash command processing.
"""

import asyncio
from typing import List, Optional

from rich.text import Text
from textual import on
from textual.containers import Horizontal, Vertical
from textual.events import Key
from textual.widgets import Input, Static, TextArea

from ai_terminal.agents.engine import Agent<PERSON>ng<PERSON>
from ai_terminal.core.config import Config
from ai_terminal.core.logger import LoggerMixin
from ai_terminal.ui.components.chat import TerminalChat
from ai_terminal.ui.components.completions import CompletionsOverlay
from ai_terminal.ui.components.multiline import MultilineEditor


class SlashCommandProcessor(LoggerMixin):
    """Processor for slash commands."""
    
    COMMANDS = {
        "/clear": "Clear current conversation",
        "/compact": "Compress conversation history", 
        "/history": "Browse conversation history",
        "/sessions": "Manage chat sessions",
        "/model": "Switch between AI models",
        "/diff": "Show file differences",
        "/help": "Show help information",
        "/config": "Configuration management",
        "/export": "Export conversations",
        "/import": "Import conversations",
        "/quit": "Exit the application",
        "/restart": "Restart the application",
        "/status": "Show system status",
        "/tools": "List available tools",
    }
    
    def __init__(self, terminal_app):
        """Initialize slash command processor."""
        self.terminal_app = terminal_app
    
    async def process_command(self, command: str) -> bool:
        """
        Process a slash command.
        
        Returns True if command was handled, False otherwise.
        """
        command = command.strip()
        
        if not command.startswith("/"):
            return False
        
        parts = command.split()
        cmd = parts[0].lower()
        args = parts[1:] if len(parts) > 1 else []
        
        try:
            if cmd == "/clear":
                await self._handle_clear()
            elif cmd == "/compact":
                await self._handle_compact()
            elif cmd == "/history":
                await self._handle_history()
            elif cmd == "/sessions":
                await self._handle_sessions()
            elif cmd == "/model":
                await self._handle_model(args)
            elif cmd == "/diff":
                await self._handle_diff()
            elif cmd == "/help":
                await self._handle_help()
            elif cmd == "/config":
                await self._handle_config(args)
            elif cmd == "/export":
                await self._handle_export(args)
            elif cmd == "/import":
                await self._handle_import(args)
            elif cmd == "/quit":
                await self._handle_quit()
            elif cmd == "/restart":
                await self._handle_restart()
            elif cmd == "/status":
                await self._handle_status()
            elif cmd == "/tools":
                await self._handle_tools()
            else:
                await self._handle_unknown(cmd)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error processing command {cmd}: {e}")
            return False
    
    async def _handle_clear(self) -> None:
        """Handle /clear command."""
        if hasattr(self.terminal_app, 'terminal_chat'):
            await self.terminal_app.terminal_chat.clear()
    
    async def _handle_compact(self) -> None:
        """Handle /compact command."""
        # TODO: Implement conversation compression
        pass
    
    async def _handle_history(self) -> None:
        """Handle /history command."""
        await self.terminal_app.action_show_history()
    
    async def _handle_sessions(self) -> None:
        """Handle /sessions command."""
        await self.terminal_app.action_show_sessions()
    
    async def _handle_model(self, args: List[str]) -> None:
        """Handle /model command."""
        await self.terminal_app.action_show_models()
    
    async def _handle_diff(self) -> None:
        """Handle /diff command."""
        await self.terminal_app.action_show_diff()
    
    async def _handle_help(self) -> None:
        """Handle /help command."""
        await self.terminal_app.action_show_help()
    
    async def _handle_config(self, args: List[str]) -> None:
        """Handle /config command."""
        # TODO: Implement config management
        pass
    
    async def _handle_export(self, args: List[str]) -> None:
        """Handle /export command."""
        # TODO: Implement conversation export
        pass
    
    async def _handle_import(self, args: List[str]) -> None:
        """Handle /import command."""
        # TODO: Implement conversation import
        pass
    
    async def _handle_quit(self) -> None:
        """Handle /quit command."""
        await self.terminal_app.action_quit()
    
    async def _handle_restart(self) -> None:
        """Handle /restart command."""
        # TODO: Implement application restart
        pass
    
    async def _handle_status(self) -> None:
        """Handle /status command."""
        # TODO: Show system status
        pass
    
    async def _handle_tools(self) -> None:
        """Handle /tools command."""
        # TODO: List available tools
        pass
    
    async def _handle_unknown(self, cmd: str) -> None:
        """Handle unknown command."""
        self.logger.warning(f"Unknown command: {cmd}")
    
    def get_completions(self, partial_command: str) -> List[str]:
        """Get command completions for partial input."""
        if not partial_command.startswith("/"):
            return []
        
        matches = [
            cmd for cmd in self.COMMANDS.keys()
            if cmd.startswith(partial_command.lower())
        ]
        
        return sorted(matches)


class TerminalChatInput(Vertical, LoggerMixin):
    """
    Advanced chat input component with multi-line support.
    
    Features:
    - Single-line and multi-line input modes
    - Syntax highlighting for code
    - Auto-completion for commands and parameters
    - Slash command processing
    - Input history navigation
    - Real-time validation
    """
    
    DEFAULT_CSS = """
    TerminalChatInput {
        height: auto;
        min-height: 3;
        max-height: 15;
        border: solid $accent;
        border-title-align: left;
    }
    
    .input-container {
        height: auto;
        padding: 1;
    }
    
    .input-field {
        width: 100%;
        height: 1;
    }
    
    .multiline-field {
        width: 100%;
        height: auto;
        min-height: 5;
    }
    
    .input-status {
        height: 1;
        background: $surface;
        color: $text;
        padding: 0 1;
    }
    """
    
    def __init__(
        self,
        config: Config,
        agent_engine: AgentEngine,
        terminal_chat: TerminalChat,
        **kwargs
    ):
        """Initialize the chat input component."""
        super().__init__(**kwargs)
        
        self.config = config
        self.agent_engine = agent_engine
        self.terminal_chat = terminal_chat
        
        # Components
        self.input_field: Optional[Input] = None
        self.multiline_editor: Optional[MultilineEditor] = None
        self.status_bar: Optional[Static] = None
        self.completions_overlay: Optional[CompletionsOverlay] = None
        
        # State
        self.is_multiline_mode = False
        self.input_history: List[str] = []
        self.history_index = -1
        self.is_processing = False
        
        # Slash command processor
        self.slash_processor = SlashCommandProcessor(self.parent)
        
        self.border_title = "💬 Type your message..."
    
    def compose(self):
        """Compose the input component."""
        with Vertical(classes="input-container"):
            # Single-line input (default)
            self.input_field = Input(
                placeholder="Type your message or /help for commands...",
                classes="input-field"
            )
            yield self.input_field
            
            # Status bar
            self.status_bar = Static("Ready", classes="input-status")
            yield self.status_bar
    
    def on_mount(self) -> None:
        """Handle component mount."""
        if self.input_field:
            self.input_field.focus()
        self.update_status("Ready • Press Ctrl+M for multi-line mode")
    
    @on(Input.Submitted)
    async def on_input_submitted(self, event: Input.Submitted) -> None:
        """Handle input submission."""
        if event.input == self.input_field:
            await self.process_input(event.value)
    
    @on(Key)
    async def on_key(self, event: Key) -> None:
        """Handle key events."""
        if event.key == "ctrl+m":
            await self.toggle_multiline_mode()
            event.prevent_default()
        elif event.key == "up" and not self.is_multiline_mode:
            self.navigate_history(-1)
            event.prevent_default()
        elif event.key == "down" and not self.is_multiline_mode:
            self.navigate_history(1)
            event.prevent_default()
        elif event.key == "tab":
            await self.show_completions()
            event.prevent_default()
        elif event.key == "escape":
            await self.hide_completions()
            event.prevent_default()
    
    async def process_input(self, text: str) -> None:
        """Process user input."""
        if not text.strip() or self.is_processing:
            return
        
        try:
            self.is_processing = True
            self.update_status("Processing...")
            
            # Add to history
            self.add_to_history(text)
            
            # Clear input
            if self.input_field:
                self.input_field.value = ""
            if self.multiline_editor:
                self.multiline_editor.clear()
            
            # Check for slash commands
            if text.startswith("/"):
                handled = await self.slash_processor.process_command(text)
                if handled:
                    self.update_status("Command executed")
                    return
            
            # Send to agent engine
            await self.send_to_agent(text)
            
        except Exception as e:
            self.logger.error(f"Error processing input: {e}")
            self.update_status(f"Error: {e}", "error")
        finally:
            self.is_processing = False
            if not self.is_processing:
                self.update_status("Ready")
    
    async def send_to_agent(self, text: str) -> None:
        """Send message to the agent engine."""
        try:
            # Add user message to chat
            from ai_terminal.storage.models import Message
            import uuid
            from datetime import datetime
            
            user_message = Message(
                id=str(uuid.uuid4()),
                session_id="current",  # Will be set by session manager
                role="user",
                content=text,
                timestamp=datetime.now()
            )
            
            await self.terminal_chat.add_message(user_message)
            
            # Start streaming response
            streaming_message = await self.terminal_chat.start_streaming_response()
            
            # Get response from agent
            response = await self.agent_engine.process_message(text)
            
            # Update streaming with response
            if response:
                await streaming_message.append_content(response.content)
                await streaming_message.finish_streaming()
                
                # Convert to regular message
                assistant_message = Message(
                    id=str(uuid.uuid4()),
                    session_id="current",
                    role="assistant",
                    content=response.content,
                    timestamp=datetime.now(),
                    model=response.model,
                    tool_calls=response.tool_calls
                )
                
                await self.terminal_chat.finish_streaming_response(assistant_message)
            
        except Exception as e:
            self.logger.error(f"Error sending to agent: {e}")
            raise
    
    async def toggle_multiline_mode(self) -> None:
        """Toggle between single-line and multi-line input modes."""
        self.is_multiline_mode = not self.is_multiline_mode
        
        if self.is_multiline_mode:
            # Switch to multi-line mode
            if self.input_field and self.input_field.parent:
                current_value = self.input_field.value
                await self.input_field.remove()
                self.input_field = None
            
            self.multiline_editor = MultilineEditor(
                config=self.config,
                classes="multiline-field"
            )
            await self.mount(self.multiline_editor, before=self.status_bar)
            
            if 'current_value' in locals():
                self.multiline_editor.text = current_value
            
            self.multiline_editor.focus()
            self.border_title = "📝 Multi-line Editor (Ctrl+Enter to send)"
            self.update_status("Multi-line mode • Ctrl+Enter to send • Ctrl+M to exit")
            
        else:
            # Switch to single-line mode
            current_value = ""
            if self.multiline_editor:
                current_value = self.multiline_editor.text
                if self.multiline_editor.parent:
                    await self.multiline_editor.remove()
                self.multiline_editor = None
            
            self.input_field = Input(
                placeholder="Type your message or /help for commands...",
                classes="input-field",
                value=current_value
            )
            await self.mount(self.input_field, before=self.status_bar)
            self.input_field.focus()
            
            self.border_title = "💬 Type your message..."
            self.update_status("Single-line mode • Press Ctrl+M for multi-line")
    
    def add_to_history(self, text: str) -> None:
        """Add text to input history."""
        if text and (not self.input_history or self.input_history[-1] != text):
            self.input_history.append(text)
            # Keep only last 100 entries
            if len(self.input_history) > 100:
                self.input_history.pop(0)
        self.history_index = len(self.input_history)
    
    def navigate_history(self, direction: int) -> None:
        """Navigate input history."""
        if not self.input_history:
            return
        
        self.history_index = max(0, min(
            len(self.input_history) - 1,
            self.history_index + direction
        ))
        
        if self.input_field and 0 <= self.history_index < len(self.input_history):
            self.input_field.value = self.input_history[self.history_index]
    
    async def show_completions(self) -> None:
        """Show auto-completions."""
        current_text = ""
        if self.input_field:
            current_text = self.input_field.value
        elif self.multiline_editor:
            current_text = self.multiline_editor.get_current_line()
        
        if current_text.startswith("/"):
            completions = self.slash_processor.get_completions(current_text)
            if completions and not self.completions_overlay:
                self.completions_overlay = CompletionsOverlay(completions)
                await self.mount(self.completions_overlay)
    
    async def hide_completions(self) -> None:
        """Hide auto-completions."""
        if self.completions_overlay and self.completions_overlay.parent:
            await self.completions_overlay.remove()
            self.completions_overlay = None
    
    def update_status(self, message: str, status_type: str = "info") -> None:
        """Update status bar message."""
        if self.status_bar:
            if status_type == "error":
                self.status_bar.update(f"❌ {message}")
            elif status_type == "success":
                self.status_bar.update(f"✅ {message}")
            elif status_type == "warning":
                self.status_bar.update(f"⚠️ {message}")
            else:
                self.status_bar.update(f"ℹ️ {message}")
    
    async def cleanup(self) -> None:
        """Cleanup input resources."""
        await self.hide_completions()
        self.logger.debug("Input component cleaned up")


class TerminalChatInputAnimativeThinking(Static, LoggerMixin):
    """
    Animated thinking indicator for AI processing states.

    Shows visual feedback when the AI is processing user input,
    generating responses, or executing tools.
    """

    DEFAULT_CSS = """
    TerminalChatInputAnimativeThinking {
        width: 100%;
        height: 1;
        background: $warning;
        color: $text;
        padding: 0 1;
    }

    .thinking-text {
        color: $text;
    }

    .thinking-spinner {
        color: $warning;
    }

    .thinking-dots {
        color: $accent;
    }
    """

    def __init__(self, **kwargs):
        """Initialize thinking indicator."""
        super().__init__(**kwargs)

        self.is_thinking = False
        self.animation_task: Optional[asyncio.Task] = None
        self.animation_frame = 0

        # Animation frames
        self.spinner_frames = ["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"]
        self.dot_frames = ["", ".", "..", "..."]

        # Thinking states
        self.thinking_states = [
            "Thinking",
            "Processing",
            "Analyzing",
            "Generating",
            "Computing",
            "Reasoning",
        ]
        self.current_state = "Thinking"

    async def start_thinking(self, state: str = "Thinking") -> None:
        """Start the thinking animation."""
        self.is_thinking = True
        self.current_state = state
        self.animation_frame = 0

        if self.animation_task:
            self.animation_task.cancel()

        self.animation_task = asyncio.create_task(self._animate())
        self.update_display()

    async def stop_thinking(self) -> None:
        """Stop the thinking animation."""
        self.is_thinking = False

        if self.animation_task:
            self.animation_task.cancel()
            self.animation_task = None

        self.update("")  # Clear display

    async def _animate(self) -> None:
        """Run the thinking animation."""
        try:
            while self.is_thinking:
                self.update_display()
                self.animation_frame = (self.animation_frame + 1) % len(self.spinner_frames)
                await asyncio.sleep(0.1)
        except asyncio.CancelledError:
            pass

    def update_display(self) -> None:
        """Update the thinking display."""
        if not self.is_thinking:
            return

        content = Text()

        # Spinner
        spinner = self.spinner_frames[self.animation_frame]
        content.append(f"{spinner} ", style="thinking-spinner")

        # State text
        content.append(f"{self.current_state}", style="thinking-text")

        # Animated dots
        dot_frame = self.animation_frame % len(self.dot_frames)
        dots = self.dot_frames[dot_frame]
        content.append(f"{dots}", style="thinking-dots")

        self.update(content)

    def set_state(self, state: str) -> None:
        """Set the current thinking state."""
        self.current_state = state
        if self.is_thinking:
            self.update_display()

    def on_unmount(self) -> None:
        """Handle component unmount."""
        if self.animation_task:
            self.animation_task.cancel()
