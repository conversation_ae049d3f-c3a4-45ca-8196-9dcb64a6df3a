"""
Database models for AI Terminal.

Defines the data structures for sessions, messages, and other persistent data
using SQLAlchemy ORM with support for encryption and indexing.
"""

import json
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from sqlalchemy import (
    Boolean, Column, DateTime, ForeignKey, Integer, String, Text, JSON
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

Base = declarative_base()


class Session(Base):
    """
    Conversation session model.
    
    Represents a conversation session with metadata and settings.
    """
    
    __tablename__ = "sessions"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String, nullable=False, index=True)
    description = Column(Text)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    last_accessed = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Session settings
    model = Column(String)
    provider = Column(String)
    system_prompt = Column(Text)
    
    # Metadata
    metadata = Column(JSON, default=dict)
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    is_archived = Column(Boolean, default=False, nullable=False)
    
    # Statistics
    message_count = Column(Integer, default=0, nullable=False)
    total_tokens = Column(Integer, default=0, nullable=False)
    
    # Relationships
    messages = relationship("Message", back_populates="session", cascade="all, delete-orphan")
    
    def __repr__(self) -> str:
        return f"<Session(id='{self.id}', name='{self.name}')>"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "last_accessed": self.last_accessed.isoformat() if self.last_accessed else None,
            "model": self.model,
            "provider": self.provider,
            "system_prompt": self.system_prompt,
            "metadata": self.metadata,
            "is_active": self.is_active,
            "is_archived": self.is_archived,
            "message_count": self.message_count,
            "total_tokens": self.total_tokens,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Session":
        """Create from dictionary representation."""
        session = cls()
        session.id = data.get("id", str(uuid.uuid4()))
        session.name = data["name"]
        session.description = data.get("description")
        session.model = data.get("model")
        session.provider = data.get("provider")
        session.system_prompt = data.get("system_prompt")
        session.metadata = data.get("metadata", {})
        session.is_active = data.get("is_active", True)
        session.is_archived = data.get("is_archived", False)
        session.message_count = data.get("message_count", 0)
        session.total_tokens = data.get("total_tokens", 0)
        
        # Parse timestamps
        if data.get("created_at"):
            session.created_at = datetime.fromisoformat(data["created_at"])
        if data.get("updated_at"):
            session.updated_at = datetime.fromisoformat(data["updated_at"])
        if data.get("last_accessed"):
            session.last_accessed = datetime.fromisoformat(data["last_accessed"])
        
        return session


class Message(Base):
    """
    Conversation message model.
    
    Represents individual messages in a conversation with content,
    metadata, and tool call information.
    """
    
    __tablename__ = "messages"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    session_id = Column(String, ForeignKey("sessions.id"), nullable=False, index=True)
    
    # Message content
    role = Column(String, nullable=False, index=True)  # user, assistant, system, tool
    content = Column(Text, nullable=False)
    
    # Timestamps
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    
    # AI model information
    model = Column(String)
    provider = Column(String)
    
    # Token usage
    prompt_tokens = Column(Integer, default=0)
    completion_tokens = Column(Integer, default=0)
    total_tokens = Column(Integer, default=0)
    
    # Tool calls and results
    tool_calls = Column(JSON)  # List of tool call data
    
    # Response metadata
    response_time = Column(Integer)  # Response time in milliseconds
    metadata = Column(JSON, default=dict)
    
    # Message status
    is_edited = Column(Boolean, default=False, nullable=False)
    is_deleted = Column(Boolean, default=False, nullable=False)
    
    # Relationships
    session = relationship("Session", back_populates="messages")
    
    def __repr__(self) -> str:
        return f"<Message(id='{self.id}', role='{self.role}', session_id='{self.session_id}')>"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "id": self.id,
            "session_id": self.session_id,
            "role": self.role,
            "content": self.content,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None,
            "model": self.model,
            "provider": self.provider,
            "prompt_tokens": self.prompt_tokens,
            "completion_tokens": self.completion_tokens,
            "total_tokens": self.total_tokens,
            "tool_calls": self.tool_calls,
            "response_time": self.response_time,
            "metadata": self.metadata,
            "is_edited": self.is_edited,
            "is_deleted": self.is_deleted,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Message":
        """Create from dictionary representation."""
        message = cls()
        message.id = data.get("id", str(uuid.uuid4()))
        message.session_id = data["session_id"]
        message.role = data["role"]
        message.content = data["content"]
        message.model = data.get("model")
        message.provider = data.get("provider")
        message.prompt_tokens = data.get("prompt_tokens", 0)
        message.completion_tokens = data.get("completion_tokens", 0)
        message.total_tokens = data.get("total_tokens", 0)
        message.tool_calls = data.get("tool_calls")
        message.response_time = data.get("response_time")
        message.metadata = data.get("metadata", {})
        message.is_edited = data.get("is_edited", False)
        message.is_deleted = data.get("is_deleted", False)
        
        # Parse timestamp
        if data.get("timestamp"):
            message.timestamp = datetime.fromisoformat(data["timestamp"])
        
        return message
    
    def get_tool_calls(self) -> List[Dict[str, Any]]:
        """Get tool calls as a list."""
        if self.tool_calls is None:
            return []
        
        if isinstance(self.tool_calls, str):
            try:
                return json.loads(self.tool_calls)
            except json.JSONDecodeError:
                return []
        
        return self.tool_calls if isinstance(self.tool_calls, list) else []
    
    def set_tool_calls(self, tool_calls: List[Dict[str, Any]]) -> None:
        """Set tool calls from a list."""
        self.tool_calls = tool_calls
    
    def add_tool_call(self, tool_call: Dict[str, Any]) -> None:
        """Add a tool call to the message."""
        current_calls = self.get_tool_calls()
        current_calls.append(tool_call)
        self.set_tool_calls(current_calls)


class UserPreference(Base):
    """
    User preferences and settings model.
    
    Stores user-specific configuration and preferences.
    """
    
    __tablename__ = "user_preferences"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    key = Column(String, nullable=False, unique=True, index=True)
    value = Column(JSON, nullable=False)
    
    # Metadata
    category = Column(String, index=True)
    description = Column(Text)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    def __repr__(self) -> str:
        return f"<UserPreference(key='{self.key}', category='{self.category}')>"


class ToolUsage(Base):
    """
    Tool usage tracking model.
    
    Tracks tool usage statistics and performance metrics.
    """
    
    __tablename__ = "tool_usage"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    tool_name = Column(String, nullable=False, index=True)
    
    # Usage details
    arguments = Column(JSON)
    result = Column(JSON)
    error = Column(Text)
    
    # Performance metrics
    execution_time = Column(Integer)  # Execution time in milliseconds
    success = Column(Boolean, nullable=False, index=True)
    
    # Context
    session_id = Column(String, ForeignKey("sessions.id"), index=True)
    message_id = Column(String, ForeignKey("messages.id"), index=True)
    
    # Timestamps
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    
    def __repr__(self) -> str:
        return f"<ToolUsage(tool_name='{self.tool_name}', success={self.success})>"
