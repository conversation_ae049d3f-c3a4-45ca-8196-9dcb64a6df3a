"""
Overlay components for AI Terminal.

Provides modal overlays for help, model selection, session management,
history browsing, diff viewing, and approval workflows.
"""

import asyncio
from typing import Callable, List, Optional

from rich.syntax import Syntax
from rich.table import Table
from rich.text import Text
from textual import on
from textual.containers import Container, Horizontal, Vertical
from textual.events import Key
from textual.widgets import Button, Input, Label, ListItem, ListView, Static

from ai_terminal.core.config import Config
from ai_terminal.core.logger import LoggerMixin


class BaseOverlay(Container, LoggerMixin):
    """Base class for all overlay components."""
    
    DEFAULT_CSS = """
    BaseOverlay {
        width: 80%;
        height: 80%;
        background: $background 95%;
        border: solid $primary;
        border-title-align: center;
        margin: 2;
        padding: 1;
    }
    
    .overlay-content {
        width: 100%;
        height: 1fr;
        padding: 1;
    }
    
    .overlay-buttons {
        width: 100%;
        height: auto;
        padding: 1;
        align: center middle;
    }
    """
    
    def __init__(self, title: str = "Overlay", **kwargs):
        """Initialize base overlay."""
        super().__init__(**kwargs)
        self.border_title = title
        self.add_class("overlay")
    
    @on(Key)
    async def on_key(self, event: Key) -> None:
        """Handle key events."""
        if event.key == "escape":
            await self.close()
            event.prevent_default()
    
    async def close(self) -> None:
        """Close the overlay."""
        if self.parent:
            await self.remove()


class TerminalChatCommandReview(Container, LoggerMixin):
    """
    Command review overlay for previewing and approving commands.

    Shows command details, potential risks, and allows users to
    approve, modify, or deny command execution.
    """

    DEFAULT_CSS = """
    TerminalChatCommandReview {
        width: 80%;
        height: auto;
        max-height: 20;
        background: $panel;
        border: solid $warning;
        border-title-align: center;
        margin: 2;
        padding: 0;
    }

    .review-header {
        height: 3;
        background: $warning;
        color: $text;
        padding: 1;
    }

    .review-content {
        padding: 1;
        background: $surface;
    }

    .command-display {
        background: $boost;
        border: solid $accent;
        padding: 1;
        margin: 1 0;
    }

    .risk-assessment {
        background: $error 20%;
        border: solid $error;
        padding: 1;
        margin: 1 0;
    }

    .review-buttons {
        height: 3;
        background: $surface;
        padding: 1;
    }

    .approve-btn {
        background: $success;
        color: $text;
    }

    .modify-btn {
        background: $warning;
        color: $text;
    }

    .deny-btn {
        background: $error;
        color: $text;
    }
    """

    def __init__(
        self,
        command: str,
        description: str = "",
        risks: List[str] = None,
        on_approve: Optional[Callable] = None,
        on_modify: Optional[Callable] = None,
        on_deny: Optional[Callable] = None,
        **kwargs
    ):
        """Initialize command review overlay."""
        super().__init__(**kwargs)

        self.command = command
        self.description = description
        self.risks = risks or []
        self.on_approve = on_approve
        self.on_modify = on_modify
        self.on_deny = on_deny

        self.review_result: Optional[str] = None
        self.review_event = asyncio.Event()
        self.modified_command: Optional[str] = None

        self.border_title = "⚠️ Command Review Required"
        self.add_class("command-review-overlay")

    def compose(self):
        """Compose command review layout."""
        with Vertical():
            # Header
            with Container(classes="review-header"):
                yield Label("Command Review Required")
                yield Label("Please review the command before execution")

            # Content
            with Container(classes="review-content"):
                # Command display
                yield Label("Command to execute:", classes="section-header")
                with Container(classes="command-display"):
                    yield Static(Syntax(self.command, "bash", theme="monokai"))

                # Description
                if self.description:
                    yield Label("Description:", classes="section-header")
                    yield Static(self.description)

                # Risk assessment
                if self.risks:
                    yield Label("Potential Risks:", classes="section-header")
                    with Container(classes="risk-assessment"):
                        for risk in self.risks:
                            yield Static(f"⚠️ {risk}")

                # Command modification area
                yield Label("Modify command (optional):", classes="section-header")
                yield Input(
                    value=self.command,
                    placeholder="Enter modified command...",
                    id="command-input"
                )

            # Buttons
            with Horizontal(classes="review-buttons"):
                yield Button("Approve", id="approve-btn", classes="approve-btn")
                yield Button("Approve Modified", id="modify-btn", classes="modify-btn")
                yield Button("Deny", id="deny-btn", classes="deny-btn")

    @on(Button.Pressed, "#approve-btn")
    async def on_approve_button(self, event: Button.Pressed) -> None:
        """Handle approve button press."""
        self.review_result = "approved"
        self.review_event.set()

        if self.on_approve:
            await self.on_approve(self.command)

        await self.close()

    @on(Button.Pressed, "#modify-btn")
    async def on_modify_button(self, event: Button.Pressed) -> None:
        """Handle modify button press."""
        try:
            command_input = self.query_one("#command-input", Input)
            self.modified_command = command_input.value

            if self.modified_command and self.modified_command != self.command:
                self.review_result = "modified"
                self.review_event.set()

                if self.on_modify:
                    await self.on_modify(self.modified_command)
            else:
                # No modification, treat as approve
                await self.on_approve_button(event)
                return

        except Exception as e:
            self.logger.error(f"Failed to get modified command: {e}")
            return

        await self.close()

    @on(Button.Pressed, "#deny-btn")
    async def on_deny_button(self, event: Button.Pressed) -> None:
        """Handle deny button press."""
        self.review_result = "denied"
        self.review_event.set()

        if self.on_deny:
            await self.on_deny(self.command)

        await self.close()

    async def get_review_result(self) -> tuple[str, Optional[str]]:
        """Wait for and return the review result."""
        await self.review_event.wait()
        return self.review_result, self.modified_command

    async def close(self) -> None:
        """Close the review overlay."""
        if self.parent:
            await self.remove()


class HelpOverlay(BaseOverlay):
    """Help overlay showing commands and shortcuts."""
    
    def __init__(self, **kwargs):
        """Initialize help overlay."""
        super().__init__(title="🆘 Help & Commands", **kwargs)
    
    def compose(self):
        """Compose help overlay content."""
        with Vertical(classes="overlay-content"):
            yield Static(self._build_help_content())
        
        with Horizontal(classes="overlay-buttons"):
            yield Button("Close", id="close-btn", variant="primary")
    
    def _build_help_content(self) -> Text:
        """Build help content."""
        content = Text()
        
        # Slash commands
        content.append("🔧 Slash Commands\n", style="bold cyan")
        content.append("─" * 50 + "\n", style="dim white")
        
        commands = [
            ("/clear", "Clear current conversation"),
            ("/history", "Browse conversation history"),
            ("/sessions", "Manage chat sessions"),
            ("/model", "Switch between AI models"),
            ("/diff", "Show file differences"),
            ("/help", "Show this help"),
            ("/config", "Configuration management"),
            ("/export", "Export conversations"),
            ("/quit", "Exit application"),
        ]
        
        for cmd, desc in commands:
            content.append(f"{cmd:<12}", style="yellow")
            content.append(f" - {desc}\n", style="white")
        
        content.append("\n")
        
        # Keyboard shortcuts
        content.append("⌨️ Keyboard Shortcuts\n", style="bold cyan")
        content.append("─" * 50 + "\n", style="dim white")
        
        shortcuts = [
            ("Ctrl+C", "Quit application"),
            ("Ctrl+N", "New session"),
            ("Ctrl+O", "Open session"),
            ("Ctrl+S", "Save session"),
            ("Ctrl+H", "Show help"),
            ("Ctrl+M", "Toggle multi-line mode"),
            ("F1-F5", "Quick access to overlays"),
            ("↑/↓", "Navigate input history"),
            ("Tab", "Show completions"),
            ("Escape", "Close overlay"),
        ]
        
        for shortcut, desc in shortcuts:
            content.append(f"{shortcut:<12}", style="green")
            content.append(f" - {desc}\n", style="white")
        
        content.append("\n")
        
        # Tips
        content.append("💡 Tips\n", style="bold cyan")
        content.append("─" * 50 + "\n", style="dim white")
        content.append("• Type naturally - the AI understands context\n", style="white")
        content.append("• Use specific commands for file operations\n", style="white")
        content.append("• Sessions auto-save your conversations\n", style="white")
        content.append("• Multi-line mode is great for code input\n", style="white")
        
        return content


class InputDialog(ModalScreen, LoggerMixin):
    """
    Input dialog for getting text input from user.

    Provides a modal dialog with text input field and submit/cancel buttons.
    """

    DEFAULT_CSS = """
    InputDialog {
        align: center middle;
    }

    .dialog-container {
        width: 60;
        height: 12;
        background: $surface;
        border: solid $primary;
        border-title-align: center;
    }

    .dialog-content {
        padding: 1;
        height: 1fr;
    }

    .dialog-buttons {
        height: 3;
        background: $panel;
        padding: 1;
    }
    """

    def __init__(
        self,
        title: str,
        prompt: str,
        placeholder: str = "",
        default_value: str = "",
        on_submit: Optional[callable] = None,
        **kwargs
    ):
        """Initialize input dialog."""
        super().__init__(**kwargs)
        self.title = title
        self.prompt = prompt
        self.placeholder = placeholder
        self.default_value = default_value
        self.on_submit = on_submit

    def compose(self):
        """Compose dialog layout."""
        with Container(classes="dialog-container"):
            self.border_title = self.title

            with Vertical(classes="dialog-content"):
                yield Label(self.prompt)
                yield Input(
                    placeholder=self.placeholder,
                    value=self.default_value,
                    id="input-field"
                )

            with Horizontal(classes="dialog-buttons"):
                yield Button("Submit", id="submit-btn", variant="primary")
                yield Button("Cancel", id="cancel-btn")

    @on(Button.Pressed, "#submit-btn")
    async def on_submit_button(self, event: Button.Pressed) -> None:
        """Handle submit button press."""
        input_field = self.query_one("#input-field", Input)
        value = input_field.value.strip()

        if self.on_submit:
            await self.on_submit(value)

        await self.dismiss()

    @on(Button.Pressed, "#cancel-btn")
    async def on_cancel_button(self, event: Button.Pressed) -> None:
        """Handle cancel button press."""
        await self.dismiss()

    @on(Input.Submitted, "#input-field")
    async def on_input_submitted(self, event: Input.Submitted) -> None:
        """Handle input field submission."""
        if self.on_submit:
            await self.on_submit(event.value.strip())
        await self.dismiss()


class ConfirmationDialog(ModalScreen, LoggerMixin):
    """
    Confirmation dialog for yes/no questions.

    Provides a modal dialog with confirmation message and yes/no buttons.
    """

    DEFAULT_CSS = """
    ConfirmationDialog {
        align: center middle;
    }

    .dialog-container {
        width: 60;
        height: 12;
        background: $surface;
        border: solid $warning;
        border-title-align: center;
    }

    .dialog-content {
        padding: 1;
        height: 1fr;
    }

    .dialog-buttons {
        height: 3;
        background: $panel;
        padding: 1;
    }
    """

    def __init__(
        self,
        title: str,
        message: str,
        on_confirm: Optional[callable] = None,
        on_cancel: Optional[callable] = None,
        **kwargs
    ):
        """Initialize confirmation dialog."""
        super().__init__(**kwargs)
        self.title = title
        self.message = message
        self.on_confirm = on_confirm
        self.on_cancel = on_cancel

    def compose(self):
        """Compose dialog layout."""
        with Container(classes="dialog-container"):
            self.border_title = self.title

            with Vertical(classes="dialog-content"):
                yield Label(self.message)

            with Horizontal(classes="dialog-buttons"):
                yield Button("Yes", id="yes-btn", variant="error")
                yield Button("No", id="no-btn", variant="default")

    @on(Button.Pressed, "#yes-btn")
    async def on_yes_button(self, event: Button.Pressed) -> None:
        """Handle yes button press."""
        if self.on_confirm:
            await self.on_confirm()
        await self.dismiss()

    @on(Button.Pressed, "#no-btn")
    async def on_no_button(self, event: Button.Pressed) -> None:
        """Handle no button press."""
        if self.on_cancel:
            await self.on_cancel()
        await self.dismiss()


class MessageDialog(ModalScreen, LoggerMixin):
    """
    Message dialog for displaying information, warnings, or errors.

    Provides a modal dialog with message and OK button.
    """

    DEFAULT_CSS = """
    MessageDialog {
        align: center middle;
    }

    .dialog-container {
        width: 60;
        height: 12;
        background: $surface;
        border: solid $primary;
        border-title-align: center;
    }

    .dialog-container.error {
        border: solid $error;
    }

    .dialog-container.warning {
        border: solid $warning;
    }

    .dialog-container.success {
        border: solid $success;
    }

    .dialog-content {
        padding: 1;
        height: 1fr;
    }

    .dialog-buttons {
        height: 3;
        background: $panel;
        padding: 1;
    }
    """

    def __init__(
        self,
        title: str,
        message: str,
        dialog_type: str = "info",  # info, error, warning, success
        on_close: Optional[callable] = None,
        **kwargs
    ):
        """Initialize message dialog."""
        super().__init__(**kwargs)
        self.title = title
        self.message = message
        self.dialog_type = dialog_type
        self.on_close = on_close

    def compose(self):
        """Compose dialog layout."""
        with Container(classes=f"dialog-container {self.dialog_type}"):
            self.border_title = self.title

            with Vertical(classes="dialog-content"):
                # Add icon based on dialog type
                icon = {
                    "info": "ℹ️",
                    "error": "❌",
                    "warning": "⚠️",
                    "success": "✅"
                }.get(self.dialog_type, "ℹ️")

                yield Label(f"{icon} {self.message}")

            with Horizontal(classes="dialog-buttons"):
                yield Button("OK", id="ok-btn", variant="primary")

    @on(Button.Pressed, "#ok-btn")
    async def on_ok_button(self, event: Button.Pressed) -> None:
        """Handle OK button press."""
        if self.on_close:
            await self.on_close()
        await self.dismiss()
    
    @on(Button.Pressed, "#close-btn")
    async def on_close_button(self, event: Button.Pressed) -> None:
        """Handle close button press."""
        await self.close()


class ModelOverlay(BaseOverlay):
    """Model selection overlay."""
    
    def __init__(self, config: Config, agent_engine, **kwargs):
        """Initialize model overlay."""
        super().__init__(title="🤖 AI Models", **kwargs)
        self.config = config
        self.agent_engine = agent_engine
        self.model_list: Optional[ListView] = None
    
    def compose(self):
        """Compose model overlay content."""
        with Vertical(classes="overlay-content"):
            yield Label("Select an AI model:")
            self.model_list = ListView()
            yield self.model_list
        
        with Horizontal(classes="overlay-buttons"):
            yield Button("Select", id="select-btn", variant="primary")
            yield Button("Cancel", id="cancel-btn")
    
    def on_mount(self) -> None:
        """Handle overlay mount."""
        self._populate_models()
    
    def _populate_models(self) -> None:
        """Populate the model list."""
        if not self.model_list:
            return
        
        providers = self.config.get("ai.providers", {})
        current_provider = self.config.get("ai.default_provider")
        current_model = self.config.get("ai.default_model")
        
        for provider_name, provider_config in providers.items():
            if not provider_config.get("enabled", True):
                continue
            
            model = provider_config.get("model", "Unknown")
            is_current = (provider_name == current_provider and model == current_model)
            
            label = f"{'🟢' if is_current else '⚪'} {provider_name}/{model}"
            if is_current:
                label += " (Current)"
            
            self.model_list.append(ListItem(Label(label), id=f"{provider_name}:{model}"))
    
    @on(Button.Pressed, "#select-btn")
    async def on_select_button(self, event: Button.Pressed) -> None:
        """Handle select button press."""
        if self.model_list and self.model_list.highlighted_child:
            model_id = self.model_list.highlighted_child.id
            if model_id and ":" in model_id:
                provider, model = model_id.split(":", 1)
                await self._switch_model(provider, model)
        await self.close()
    
    @on(Button.Pressed, "#cancel-btn")
    async def on_cancel_button(self, event: Button.Pressed) -> None:
        """Handle cancel button press."""
        await self.close()
    
    async def _switch_model(self, provider: str, model: str) -> None:
        """Switch to selected model."""
        try:
            self.config.set("ai.default_provider", provider)
            self.config.set("ai.default_model", model)
            
            if self.agent_engine:
                await self.agent_engine.switch_model(provider, model)
            
        except Exception as e:
            self.logger.error(f"Failed to switch model: {e}")


class SessionsOverlay(BaseOverlay):
    """Session management overlay."""
    
    def __init__(self, session_manager, on_session_selected: Callable, **kwargs):
        """Initialize sessions overlay."""
        super().__init__(title="💬 Sessions", **kwargs)
        self.session_manager = session_manager
        self.on_session_selected = on_session_selected
        self.session_list: Optional[ListView] = None
    
    def compose(self):
        """Compose sessions overlay content."""
        with Vertical(classes="overlay-content"):
            yield Label("Select a session:")
            self.session_list = ListView()
            yield self.session_list
        
        with Horizontal(classes="overlay-buttons"):
            yield Button("Open", id="open-btn", variant="primary")
            yield Button("New", id="new-btn", variant="success")
            yield Button("Delete", id="delete-btn", variant="error")
            yield Button("Cancel", id="cancel-btn")
    
    async def on_mount(self) -> None:
        """Handle overlay mount."""
        await self._populate_sessions()
    
    async def _populate_sessions(self) -> None:
        """Populate the session list."""
        if not self.session_list:
            return
        
        try:
            sessions = await self.session_manager.list_sessions()
            current_session = await self.session_manager.get_current_session()
            
            for session in sessions:
                is_current = current_session and session.id == current_session.id
                label = f"{'🟢' if is_current else '⚪'} {session.name}"
                if is_current:
                    label += " (Current)"
                
                self.session_list.append(ListItem(Label(label), id=session.id))
                
        except Exception as e:
            self.logger.error(f"Failed to populate sessions: {e}")
    
    @on(Button.Pressed, "#open-btn")
    async def on_open_button(self, event: Button.Pressed) -> None:
        """Handle open button press."""
        if self.session_list and self.session_list.highlighted_child:
            session_id = self.session_list.highlighted_child.id
            if session_id:
                session = await self.session_manager.get_session(session_id)
                if session:
                    await self.on_session_selected(session.name)
    
    @on(Button.Pressed, "#new-btn")
    async def on_new_button(self, event: Button.Pressed) -> None:
        """Handle new button press."""
        # Create input dialog for session name
        dialog = InputDialog(
            title="New Session",
            prompt="Enter session name:",
            placeholder="my_session",
            on_submit=self._on_new_session_name
        )
        await self.mount(dialog)

    async def _on_new_session_name(self, session_name: str) -> None:
        """Handle new session name input."""
        if not session_name.strip():
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            session_name = f"session_{timestamp}"

        try:
            await self.session_manager.create_session(session_name.strip())
            await self._populate_sessions()
        except Exception as e:
            # Show error message
            error_dialog = MessageDialog(
                title="Error",
                message=f"Failed to create session: {e}",
                dialog_type="error"
            )
            await self.mount(error_dialog)

    @on(Button.Pressed, "#delete-btn")
    async def on_delete_button(self, event: Button.Pressed) -> None:
        """Handle delete button press."""
        if self.session_list and self.session_list.highlighted_child:
            session_id = self.session_list.highlighted_child.id
            if session_id:
                # Show confirmation dialog
                confirm_dialog = ConfirmationDialog(
                    title="Delete Session",
                    message="Are you sure you want to delete this session? This action cannot be undone.",
                    on_confirm=lambda: self._delete_session(session_id)
                )
                await self.mount(confirm_dialog)

    async def _delete_session(self, session_id: str) -> None:
        """Delete a session after confirmation."""
        try:
            await self.session_manager.delete_session(session_id)
            await self._populate_sessions()
        except Exception as e:
            # Show error message
            error_dialog = MessageDialog(
                title="Error",
                message=f"Failed to delete session: {e}",
                dialog_type="error"
            )
            await self.mount(error_dialog)
    
    @on(Button.Pressed, "#cancel-btn")
    async def on_cancel_button(self, event: Button.Pressed) -> None:
        """Handle cancel button press."""
        await self.close()


class HistoryOverlay(BaseOverlay):
    """Conversation history overlay."""
    
    def __init__(self, session_manager, **kwargs):
        """Initialize history overlay."""
        super().__init__(title="📜 History", **kwargs)
        self.session_manager = session_manager
        self.history_list: Optional[ListView] = None
    
    def compose(self):
        """Compose history overlay content."""
        with Vertical(classes="overlay-content"):
            yield Label("Conversation History:")
            self.history_list = ListView()
            yield self.history_list
        
        with Horizontal(classes="overlay-buttons"):
            yield Button("Close", id="close-btn", variant="primary")
    
    async def on_mount(self) -> None:
        """Handle overlay mount."""
        await self._populate_history()
    
    async def _populate_history(self) -> None:
        """Populate the history list."""
        if not self.history_list:
            return
        
        try:
            current_session = await self.session_manager.get_current_session()
            if current_session:
                messages = await self.session_manager.get_session_messages(current_session.id)
                
                for message in messages[-50:]:  # Show last 50 messages
                    timestamp = message.timestamp.strftime("%H:%M:%S")
                    role_icon = "👤" if message.role == "user" else "🤖"
                    preview = message.content[:100] + "..." if len(message.content) > 100 else message.content
                    
                    label = f"{role_icon} {timestamp} - {preview}"
                    self.history_list.append(ListItem(Label(label)))
                    
        except Exception as e:
            self.logger.error(f"Failed to populate history: {e}")
    
    @on(Button.Pressed, "#close-btn")
    async def on_close_button(self, event: Button.Pressed) -> None:
        """Handle close button press."""
        await self.close()


class DiffOverlay(BaseOverlay):
    """File diff viewing overlay."""
    
    def __init__(self, **kwargs):
        """Initialize diff overlay."""
        super().__init__(title="📊 File Differences", **kwargs)
    
    def compose(self):
        """Compose diff overlay content."""
        with Vertical(classes="overlay-content"):
            yield Static("No diffs to display", id="diff-content")
        
        with Horizontal(classes="overlay-buttons"):
            yield Button("Close", id="close-btn", variant="primary")
    
    @on(Button.Pressed, "#close-btn")
    async def on_close_button(self, event: Button.Pressed) -> None:
        """Handle close button press."""
        await self.close()


class ApprovalModeOverlay(BaseOverlay):
    """Command approval overlay for security."""
    
    def __init__(self, command: str, description: str, **kwargs):
        """Initialize approval overlay."""
        super().__init__(title="⚠️ Approval Required", **kwargs)
        self.command = command
        self.description = description
        self.approval_result: Optional[bool] = None
        self.approval_event = asyncio.Event()
    
    def compose(self):
        """Compose approval overlay content."""
        with Vertical(classes="overlay-content"):
            yield Label("The AI wants to execute a potentially dangerous command:")
            yield Static(f"\nCommand: {self.command}", id="command-display")
            yield Static(f"\nDescription: {self.description}", id="description-display")
            yield Label("\nDo you want to allow this?")
        
        with Horizontal(classes="overlay-buttons"):
            yield Button("Allow", id="allow-btn", variant="success")
            yield Button("Deny", id="deny-btn", variant="error")
    
    @on(Button.Pressed, "#allow-btn")
    async def on_allow_button(self, event: Button.Pressed) -> None:
        """Handle allow button press."""
        self.approval_result = True
        self.approval_event.set()
        await self.close()
    
    @on(Button.Pressed, "#deny-btn")
    async def on_deny_button(self, event: Button.Pressed) -> None:
        """Handle deny button press."""
        self.approval_result = False
        self.approval_event.set()
        await self.close()
    
    async def get_approval(self) -> bool:
        """Wait for and return approval result."""
        await self.approval_event.wait()
        return self.approval_result or False
