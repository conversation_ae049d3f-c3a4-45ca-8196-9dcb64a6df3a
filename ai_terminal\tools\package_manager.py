"""
Package manager tool for AI Terminal.

Provides package management operations for multiple package managers
including pip, npm, cargo, and system package managers.
"""

from typing import Any, Dict

from ai_terminal.tools.base import BaseTool, ToolExecutionError


class PackageManagerTool(BaseTool):
    """Package management tool for multiple package managers."""
    
    def __init__(self):
        """Initialize package manager tool."""
        super().__init__(
            name="package_manager",
            description="Package management for pip, npm, cargo, and system packages"
        )
        self.requires_approval = True  # Package operations can be dangerous
    
    async def execute(self, manager: str, operation: str, **kwargs) -> Any:
        """Execute a package manager operation."""
        managers = {
            "pip": self._pip_operation,
            "npm": self._npm_operation,
            "cargo": self._cargo_operation,
            "apt": self._apt_operation,
            "brew": self._brew_operation,
        }
        
        if manager not in managers:
            raise ToolExecutionError(f"Unknown package manager: {manager}", self.name)
        
        return await managers[manager](operation, **kwargs)
    
    async def _pip_operation(self, operation: str, **kwargs) -> Dict[str, Any]:
        """Execute pip operations."""
        try:
            import subprocess

            package = kwargs.get("package", "")
            version = kwargs.get("version", "")

            if operation == "install":
                if not package:
                    return {"error": "Package name required for install", "success": False}

                cmd = ["pip", "install"]
                if version:
                    cmd.append(f"{package}=={version}")
                else:
                    cmd.append(package)

                # Add common flags
                if kwargs.get("upgrade", False):
                    cmd.append("--upgrade")
                if kwargs.get("user", False):
                    cmd.append("--user")

            elif operation == "uninstall":
                if not package:
                    return {"error": "Package name required for uninstall", "success": False}
                cmd = ["pip", "uninstall", package, "-y"]

            elif operation == "list":
                cmd = ["pip", "list"]
                if kwargs.get("outdated", False):
                    cmd.append("--outdated")

            elif operation == "show":
                if not package:
                    return {"error": "Package name required for show", "success": False}
                cmd = ["pip", "show", package]

            elif operation == "search":
                if not package:
                    return {"error": "Search term required", "success": False}
                # Note: pip search was disabled, using alternative
                return {"error": "pip search is currently disabled", "success": False}

            elif operation == "upgrade":
                cmd = ["pip", "install", "--upgrade", "pip"]

            else:
                return {"error": f"Unknown pip operation: {operation}", "success": False}

            # Execute command
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=120
            )

            return {
                "manager": "pip",
                "operation": operation,
                "success": result.returncode == 0,
                "output": result.stdout.strip(),
                "error": result.stderr.strip() if result.returncode != 0 else None,
                "package": package,
                "version": version
            }

        except subprocess.TimeoutExpired:
            return {"error": "Pip command timed out", "success": False, "manager": "pip"}
        except FileNotFoundError:
            return {"error": "pip not found in PATH", "success": False, "manager": "pip"}
        except Exception as e:
            self.logger.error(f"Pip operation failed: {e}")
            return {"error": str(e), "success": False, "manager": "pip"}
    
    async def _npm_operation(self, operation: str, **kwargs) -> Dict[str, Any]:
        """Execute npm operations."""
        # TODO: Implement npm operations
        operations = ["install", "uninstall", "list", "info", "search", "update"]
        if operation not in operations:
            raise ToolExecutionError(f"Unknown npm operation: {operation}", self.name)
        
        return {"manager": "npm", "operation": operation, "success": True}
    
    async def _cargo_operation(self, operation: str, **kwargs) -> Dict[str, Any]:
        """Execute cargo operations."""
        # TODO: Implement cargo operations
        operations = ["install", "uninstall", "search", "update"]
        if operation not in operations:
            raise ToolExecutionError(f"Unknown cargo operation: {operation}", self.name)
        
        return {"manager": "cargo", "operation": operation, "success": True}
    
    async def _apt_operation(self, operation: str, **kwargs) -> Dict[str, Any]:
        """Execute apt operations."""
        # TODO: Implement apt operations
        operations = ["install", "remove", "update", "upgrade", "search", "show"]
        if operation not in operations:
            raise ToolExecutionError(f"Unknown apt operation: {operation}", self.name)
        
        return {"manager": "apt", "operation": operation, "success": True}
    
    async def _brew_operation(self, operation: str, **kwargs) -> Dict[str, Any]:
        """Execute brew operations."""
        # TODO: Implement brew operations
        operations = ["install", "uninstall", "update", "upgrade", "search", "info"]
        if operation not in operations:
            raise ToolExecutionError(f"Unknown brew operation: {operation}", self.name)
        
        return {"manager": "brew", "operation": operation, "success": True}
    
    def get_schema(self) -> Dict[str, Any]:
        """Get JSON schema for package manager tool."""
        return {
            "name": "package_manager",
            "description": "Package management operations",
            "parameters": {
                "type": "object",
                "properties": {
                    "manager": {
                        "type": "string",
                        "enum": ["pip", "npm", "cargo", "apt", "brew"],
                        "description": "Package manager to use"
                    },
                    "operation": {
                        "type": "string",
                        "enum": ["install", "uninstall", "list", "search", "update", "upgrade"],
                        "description": "Package operation to perform"
                    },
                    "package": {
                        "type": "string",
                        "description": "Package name for install/uninstall operations"
                    },
                    "version": {
                        "type": "string",
                        "description": "Specific package version"
                    }
                },
                "required": ["manager", "operation"]
            }
        }
